# TheDenAntiCheat Plugin

A sophisticated anti-cheat system for Rust servers using the uMod/Oxide framework. This plugin provides advanced statistical analysis and physics-based detection methods to identify aimbot, recoil cheats, rapid fire, and wallhack usage in PvP combat scenarios.

## 📋 Overview

**Version:** 2.4.0  
**Author:** Golgolak  
**Framework:** uMod/Oxide  
**Game:** Rust

## 🆕 Latest Updates (v2.4.0)

### ✨ New Features
- **🔍 Comprehensive Recoil Debug System**: Advanced 47-column CSV logging with complete calculation transparency
- **Daily Violation Stats Tracking**: Automatic daily reset of violation counters at midnight UTC
- **Console Commands**: Added `clearstats` admin command to reset all violation statistics
- **Enhanced Detection Logic**: Proper fallback behavior between enhanced and legacy recoil detection
- **Improved Violation Types**: Clear distinction between "RecoilPattern" and "NoRecoilLegacy" violations

### 🔧 Improvements
- **Smart Detection Routing**: No more redundant double detection - each weapon uses exactly one detection method
- **Better Logging Clarity**: Violation types now clearly indicate which detection method was used
- **Admin Tools**: New console command for managing violation statistics
- **API Enhancements**: Updated violation stats API with daily tracking data
- **Debug Analysis Tools**: Complete visibility into why violations are/aren't being detected

### 🛠️ Technical Fixes
- **Fixed Detection Overlap**: Eliminated simultaneous "NoRecoil" and "RecoilPattern" violations
- **Proper Fallback Logic**: Enhanced detection for weapons with data, legacy for unknown weapons
- **Clean Code**: Removed redundant detection methods for better maintainability

TheDenAntiCheat is a comprehensive server-side anti-cheat plugin that monitors PvP combat through real-time analysis of player behavior, weapon statistics, and physics calculations. It features dynamic weapon data integration and multi-layered detection algorithms to identify suspicious activity with minimal false positives.

## ✨ Features

### 🎯 Ranged Weapons Only Detection
**All cheat detection is limited to ranged weapons only** - melee weapons, tools, and throwables are automatically excluded from analysis to prevent false positives and focus on relevant PvP scenarios.

#### **Supported Ranged Weapon Categories:**
- **Assault Rifles**: AK-47, LR-300, M39 EMR, Semi-Automatic Rifle
- **SMGs**: Custom SMG, Thompson, MP5
- **Pistols**: M92, Python, Revolver, Semi-Automatic Pistol, Eoka, Nailgun
- **Shotguns**: Pump Shotgun, Waterpipe Shotgun, Double Barrel, M4 Shotgun, SPAS-12
- **Sniper Rifles**: Bolt Action Rifle, L96
- **Crossbows & Bows**: Crossbow, Hunting Bow, Compound Bow
- **Launchers**: Rocket Launcher, Multiple Grenade Launcher
- **Machine Guns**: M249, Heavy Machine Gun, Minigun

#### **Excluded Weapons (No Detection):**
- **Melee Weapons**: Swords, spears, maces, bone clubs, salvaged cleaver
- **Tools**: Hatchets, pickaxes, hammers, chainsaw, jackhammer
- **Throwables**: F1 grenades, bean can grenades, satchel charges
- **Special Items**: Flamethrower, medical syringes, repair tools
- **Building Tools**: Building plans, hammers, wire tools

### 🎯 Aimbot Detection
- Statistical hit-to-shot ratio analysis in PvP encounters (ranged weapons only)
- Configurable threshold system (default: 85% hit ratio)
- Minimum shot requirement to prevent false positives (default: 10 shots)
- Automatic data cleanup and rolling analysis

### 🔫 Advanced Weapon-Aware Recoil Detection System
The plugin implements a sophisticated multi-layered recoil detection system with weapon-specific intelligence:

#### **Weapon-Aware Pattern Analysis**
- **Dynamic thresholds** calculated from each weapon's natural recoil characteristics
- **Percentage-based tolerance** system (configurable deviation from weapon's expected recoil)
- Statistical analysis of recoil variance and consistency
- Calculates standard deviation of aim angle changes
- **Fair detection**: High-recoil weapons (AK-47) get higher thresholds, low-recoil weapons (MP5) get stricter thresholds

#### **Legacy Pattern Detection**
- Fallback detection when weapon data is unavailable
- Uses fixed baseline with percentage-based tolerance
- Maintains backwards compatibility

#### **Legacy Angle Detection**
- Basic no-recoil detection for consecutive shots
- Monitors rapid-fire angle changes
- Works as additional validation layer

### 🧱 Wallhack Detection
- Physics-based raycast analysis from shooter to target (ranged weapons only)
- Real-time collision detection against terrain and structures
- Ignores legitimate penetration scenarios
- Excludes NPC interactions to prevent false positives

### 📊 Dynamic Weapon Data Integration
- Automatic loading from `oxide/data/thedenproject/weapon_dump.json`
- Daily refresh mechanism with file modification checking
- Supports 17+ weapon types with specific characteristics:
  - RPM (Rate of Fire) for timing analysis
  - Yaw/Pitch recoil bounds for validation
  - Aimcone and magazine data for enhanced detection
  - Automatic vs semi-automatic weapon classification

### 📁 Enhanced Smart Logging System
- Daily rotating CSV log files with YYYYMMDD format
- Advanced buffer-based writing system for optimal performance
- Smart timer-based flushing with configurable intervals (default: 300 seconds)
- Buffer size limit monitoring with immediate flush (default: 256KB)
- Timer starts only when buffer has content for maximum efficiency
- Configurable console logging control
- Debug mode buffer size monitoring
- Comprehensive violation tracking with detailed metadata
- Automatic directory creation and file management

### 🛡️ Smart PvP-Only Monitoring
- Focuses exclusively on human vs human combat
- Excludes NPC interactions and PvE scenarios
- Prevents false positives from legitimate gameplay
- Optional debug mode for comprehensive testing

### 📈 Ultra-Lightweight Violation Stats System
- **Repeat offender tracking** with Steam ID-based lookups
- **Lazy initialization** - only tracks players with violations (zero overhead for clean players)
- **Violation type breakdown** - tracks Aimbot, RecoilPattern, NoRecoil, Wallhack separately
- **First/Last violation timestamps** for pattern analysis
- **API integration** for external plugin access
- **Piggybacks on existing logging system** - no additional performance impact

### 🔧 Debug Mode
- Monitors all weapon usage (not just PvP combat)
- Adds [DEBUG] prefix to all violation logs
- Useful for testing and configuration tuning
- Configurable via DebugMode setting

### 🔍 Comprehensive Recoil Debug System
- **Complete Calculation Transparency**: Logs every piece of data used in recoil detection
- **47-Column CSV Output**: Captures raw inputs, calculations, thresholds, and decisions
- **Zero Performance Impact**: Only active when enabled, smart 128KB buffering
- **Perfect for Analysis**: Identify exactly why violations aren't being detected
- **Both Detection Methods**: Covers Enhanced (weapon-aware) and Legacy detection
- **Smart Logging**: 30-second flush intervals with automatic date rotation
- **File Location**: `oxide/logs/thedenproject/recoil_debug_YYYYMMDD.csv`

## 📦 Installation

### Prerequisites
1. Rust server with uMod/Oxide framework installed
2. **RustDumper plugin** (required for weapon data generation)

### Installation Steps
1. Download `TheDenAntiCheat.cs` from this repository
2. Place the file in your server's `oxide/plugins/` directory
3. Install and load the RustDumper plugin
4. Run `dumpweapon` command in server console to generate weapon data
5. Restart your server or use `oxide.reload TheDenAntiCheat` in console
6. Configure the plugin using the generated config file at `oxide/config/TheDenAntiCheat.json`

### Weapon Data Setup
The plugin requires weapon data for enhanced detection:
```bash
# In server console
oxide.load RustDumper
dumpweapon
```
This creates `oxide/data/thedenproject/weapon_dump.json` with current weapon statistics.

## ⚙️ Configuration

The plugin generates a comprehensive configuration file at `oxide/config/TheDenAntiCheat.json`:

### Complete Configuration Reference
```json
{
  "General": {
    "EnableAimbotDetection": true,
    "EnableRecoilDetection": true,
    "EnableWallhackDetection": true,
    "EnableViolationStats": true,
    "DebugMode": false,
    "DebugRecoil": false
  },
  "Aimbot": {
    "MaxHitRatio": 0.85,
    "MinShots": 10
  },
  "Recoil": {
    "MaxAngleChange": 0.1,
    "VarianceThreshold": 0.1,
    "AnalysisMinShots": 10,
    "EnablePatternDetection": true
  },
  "Logging": {
    "EnableLogging": true,
    "EnableConsoleLogging": false,
    "SaveFrequency": 300.0,
    "MaxBufferSizeKB": 256.0
  }
}
```

### Enhanced Configuration System

The plugin features a robust configuration system with automatic migration and validation:

#### **Automatic Migration**
- **Backwards Compatibility**: Seamlessly upgrades from older config formats
- **User Setting Preservation**: Migrates existing user customizations automatically
- **Clear Feedback**: Provides console messages about migration status
- **Graceful Fallbacks**: Uses safe defaults if migration fails

#### **Configuration Validation**
- **Range Checking**: Validates all percentage values (0.0-1.0)
- **Positive Value Validation**: Ensures intervals and sizes are positive
- **Auto-Correction**: Automatically fixes invalid values with user warnings
- **Integrity Checks**: Validates config structure before loading

#### **Migration Process**
When the plugin detects an old or corrupted configuration:
1. **Preserves** all valid user settings from the old config
2. **Corrects** any invalid values with appropriate defaults
3. **Adds** new configuration options with recommended settings
4. **Provides** clear console feedback about changes made

### Configuration Options Explained

#### **General Detection Settings**
- `EnableAimbotDetection`: Toggle aimbot detection on/off
- `EnableRecoilDetection`: Toggle all recoil detection methods on/off
- `EnableWallhackDetection`: Toggle wallhack detection on/off
- `EnableViolationStats`: Toggle violation statistics tracking for repeat offenders
- `DebugMode`: Monitor all combat (not just PvP) and add [DEBUG] prefix to logs
- `DebugRecoil`: **Enable comprehensive recoil debug logging** - logs all calculation data to separate CSV file

#### **Aimbot Detection Settings**
- `MaxHitRatio`: Maximum allowed hit ratio before flagging (0.0-1.0)
- `MinShots`: Minimum shots required before analyzing hit ratio

#### **Recoil Detection Settings**
- `MaxAngleChange`: Maximum aim angle change allowed between shots (legacy detection)
- `VarianceThreshold`: **Tolerance percentage for weapon-aware detection** (0.1 = 10% deviation allowed from weapon's natural recoil)
- `AnalysisMinShots`: Minimum shots required for pattern analysis
- `EnablePatternDetection`: Toggle variance-based pattern detection

#### **Enhanced Logging Settings**
- `EnableLogging`: Toggle CSV file logging on/off
- `EnableConsoleLogging`: Toggle console violation logging on/off (default: false)
- `SaveFrequency`: Smart buffer flush interval in seconds (default: 300)
- `MaxBufferSizeKB`: Buffer size limit before immediate flush (default: 256KB)

## 🔐 Permissions

### thedenanticheat.bypass
Grants complete immunity from all cheat detection monitoring.

**Grant to user:**
```
oxide.grant user <username> thedenanticheat.bypass
```

**Grant to group:**
```
oxide.grant group <groupname> thedenanticheat.bypass
```

## 📁 Log Files

### Location and Format
Logs are stored in: `oxide/data/thedenproject/detected_violations_YYYYMMDD.csv`

### Enhanced CSV Structure
```csv
Timestamp,PlayerID,PlayerName,TargetID,TargetName,ViolationType,Weapon,Details
2025-06-07 16:30:15,76561198123456789,PlayerName,76561198987654321,TargetName,Aimbot,rifle.ak,PvP hit ratio 0.92 exceeds threshold 0.85
2025-06-07 16:31:22,76561198123456789,PlayerName,76561198987654321,TargetName,RecoilPattern,rifle.ak,"Recoil variance 0.15 below weapon threshold 1.89 (expected range: 2.1, player range: 0.15, deviation: -92.9%, tolerance: 10%, shots: 12, mean: 0.45)"
2025-06-07 16:32:05,76561198123456789,PlayerName,76561198987654321,TargetName,RecoilPattern,smg.mp5,"Recoil variance 0.45 below legacy threshold 0.72 (no weapon data, player range: 0.6, tolerance: 10%, shots: 10, mean: 0.52)"
2025-06-07 16:32:10,76561198123456789,PlayerName,76561198987654321,TargetName,NoRecoil,rifle.ak,Angle change 0.05 below threshold 0.10 for rifle.ak
```

#### **Enhanced Violation Details:**
- **Expected Range**: Weapon's natural recoil characteristics from weapon data
- **Player Range**: Actual recoil range exhibited by the player
- **Deviation**: Percentage difference from expected (-92.9% = 92.9% smoother than expected)
- **Tolerance**: Configured tolerance percentage for detection sensitivity
- **Legacy Fallback**: Clear indication when weapon data is unavailable

### Violation Types
The plugin generates the following violation types:
- **Aimbot**: Excessive hit ratio detection
- **RecoilPattern**: Unnaturally consistent recoil patterns (enhanced weapon-aware detection)
- **NoRecoilLegacy**: Insufficient recoil variation (legacy angle-based detection)
- **Wallhack**: Shots through solid obstacles

### Log Management
- New log file created daily at midnight UTC
- Files named with YYYYMMDD format for easy organization
- Automatic directory creation
- Buffered writing system for optimal performance
- Old shot data automatically cleaned up (9-second retention)

## 📈 Violation Statistics

### Storage Location
Violation statistics are stored in: `oxide/data/thedenproject/violationstats.json`

### API Integration
The plugin provides API hooks for external plugin integration:

#### **GetPlayerViolationStats(steamId)**
Returns detailed violation statistics for a specific player:
```csharp
var stats = TheDenAntiCheat?.Call("GetPlayerViolationStats", "76561198123456789");
// Returns: playerName, totalViolations, violationsByType, firstViolation, lastViolation,
//          violationsToday, violationsByTypeToday, currentStatsDate
```

#### **GetTopViolators(count)**
Returns list of top violators on the server:
```csharp
var topViolators = TheDenAntiCheat?.Call("GetTopViolators", 10);
// Returns: List of top 10 violators with steamId, playerName, totalViolations, 
//          violationsToday, lastViolation
```

## 🔧 Console Commands

### clearstats (Admin Only)
Resets all violation statistics for all players on the server.

**Usage:**
```bash
clearstats
```

**Features:**
- Requires admin privileges (auth level 2+)
- Clears in-memory violation statistics cache
- Deletes the violation stats data file from disk
- Shows confirmation with count of cleared player records
- Includes debug logging when debug mode is enabled

**Example:**
```bash
> clearstats
✅ Cleared violation stats for 42 players.
```

### Statistics Features
- **Lazy Initialization**: Only tracks players with violations (zero overhead for clean players)
- **Steam ID Indexing**: Fast lookups using ulong Steam IDs
- **Violation Type Breakdown**: Separate counters for Aimbot, RecoilPattern, NoRecoil, Wallhack
- **Timestamp Tracking**: First and last violation dates for pattern analysis
- **Automatic Saves**: Piggybacks on existing logging system for efficiency

## 🔧 Technical Implementation

### Performance Optimization
- Lightweight design with minimal server impact
- Event-driven detection architecture (no periodic polling)
- Player data cleanup on disconnect
- Smart buffered logging with size limits and configurable intervals
- Efficient shot history management with automatic cleanup

### Detection Algorithms

#### **Aimbot Detection Algorithm**
1. Tracks shot-to-hit ratios over time per player
2. Requires minimum shot count to prevent false positives
3. Compares against configurable threshold
4. Resets data on player disconnect

#### **Recoil Detection Algorithms**
1. **Pattern Analysis**: Calculates standard deviation of aim angle changes
2. **Legacy Detection**: Basic angle change monitoring as fallback

#### **Wallhack Detection Algorithm**
1. Performs raycast from shooter position to hit position
2. Checks for solid obstacles using layer masks (Terrain, World, Construction)
3. Excludes target player object from collision detection
4. Logs violations when obstacles are detected in bullet path

### Weapon Data Integration
The plugin automatically loads weapon statistics from the RustDumper output:
- **File Location**: `oxide/data/thedenproject/weapon_dump.json`
- **Refresh Mechanism**: Daily automatic reload based on file modification time
- **Fallback Behavior**: Graceful degradation when weapon data unavailable
- **Supported Properties**: RPM, recoil bounds, aimcone, magazine size, reload time

### Memory Management
- Shot history limited to 9 seconds per player
- Automatic cleanup of disconnected player data
- Efficient data structures for minimal memory footprint
- Buffer-based logging to prevent I/O blocking

## 🐛 Troubleshooting

### Common Issues

#### **Plugin Not Loading**
- Ensure uMod/Oxide framework is properly installed and updated
- Check server console for syntax errors or missing dependencies
- Verify file permissions on the plugins directory

#### **No Weapon Data / Enhanced Detection Not Working**
```bash
# Install RustDumper plugin first
oxide.load RustDumper
# Generate weapon data
dumpweapon
# Verify file creation
ls oxide/data/thedenproject/weapon_dump.json
```

#### **No Logs Being Generated**
- Check `EnableLogging: true` in configuration
- Verify write permissions for `oxide/data/thedenproject/` directory
- Monitor server console for I/O error messages
- Check `LogSaveFrequency` setting (default: 60 seconds)
- Ensure violations are occurring to trigger the smart buffer timer

#### **False Positives**
- Adjust `MaxHitRatio` threshold in Aimbot section (increase from 0.85)
- Increase `MinShots` requirement in Aimbot section (default: 10)
- Adjust `VarianceThreshold` in Recoil section for sensitivity
- Grant `thedenanticheat.bypass` permission to skilled players
- Enable `DebugMode` temporarily to analyze detection behavior

#### **Missing Violation Types**
- Ensure weapon data is loaded (`dumpweapon` command executed)
- Check that specific detection types are enabled in config
- Verify minimum shot requirements are met for analysis

### Debug Mode Usage
Enable debug mode for comprehensive analysis:
```json
{
  "DebugMode": true
}
```
This will:
- Monitor all weapon usage (not just PvP)
- Add [DEBUG] prefix to all logs
- Help identify configuration issues
- Useful for testing threshold adjustments

### Recoil Debug Analysis
Enable comprehensive recoil debugging for missed detection analysis:
```json
{
  "DebugRecoil": true
}
```

#### **How to Use Recoil Debug Data:**

1. **Enable Debug Logging**: Set `"DebugRecoil": true` in config
2. **Generate Test Data**: Have suspected players use weapons in combat
3. **Analyze Debug CSV**: Open `oxide/logs/thedenproject/recoil_debug_YYYYMMDD.csv`

#### **Key Debug Columns to Check:**
- `WAS_DETECTED`: Whether violation was triggered (true/false)
- `VIOLATION_REASON`: Exact reason if detected
- `DETECTION_METHOD`: Enhanced vs Legacy detection used
- `PASSED_SUFFICIENT_SHOTS_CHECK`: Whether minimum shot requirement met
- `CALCULATED_STD_DEV` vs `CALCULATED_THRESHOLD`: Variance analysis
- `WEAPON_HAS_DATA`: Whether weapon data is available
- `PLAYER_RECOIL_RANGE` vs `WEAPON_EXPECTED_RECOIL_RANGE`: Player vs expected recoil

#### **Common Debug Scenarios:**

**No Violations Detected:**
- Check `PASSED_SUFFICIENT_SHOTS_CHECK` = false → Increase shots taken
- Check `WEAPON_HAS_DATA` = false → Run `dumpweapon` command
- Check `CALCULATED_STD_DEV` > `CALCULATED_THRESHOLD` → Player recoil is varied enough

**False Positives:**
- Check `DEVIATION_PERCENTAGE` → Large negative values indicate overly strict thresholds
- Examine `ANGLE_CHANGES_LIST` → Look for natural variation patterns
- Review `CONFIG_VARIANCE_THRESHOLD` → Consider increasing tolerance

**Detection Method Issues:**
- `DETECTION_METHOD` = "Legacy" when Enhanced expected → Missing weapon data
- Low `VALID_ANGLE_CHANGES` count → Not enough valid recoil data collected

#### **Performance Notes:**
- Debug logging has **zero overhead** when disabled
- Uses separate 128KB buffer with 30-second flush intervals
- Automatically rotates daily like main violation logs

## 🔍 Monitoring and Analysis

### Log Analysis Tips
1. **High Aimbot Ratios**: Look for consistent 90%+ hit ratios across multiple sessions
2. **Rapid Fire Patterns**: Check for systematic fire rate violations with same weapon
3. **Recoil Consistency**: Multiple RecoilPattern violations indicate scripted compensation
4. **Wallhack Clusters**: Multiple wallhack violations in short timeframes are highly suspicious

### Recommended Thresholds
Based on legitimate player analysis:
- **Aimbot Ratio**: 0.85 (85%) for most servers, 0.90 (90%) for hardcore/experienced servers
- **Recoil Variance**: 0.3 (30%) standard, 0.2 (20%) for stricter detection
- **Min Shots**: 10-15 shots minimum for reliable statistical analysis

## 🚀 Advanced Configuration

### Weapon-Specific Tuning
The plugin automatically adapts to weapon characteristics:
- **High-recoil weapons** (AK-47, LR-300): More lenient pattern detection
- **Automatic weapons**: Stricter rapid-fire detection
- **High-RPM weapons**: Enhanced timing analysis
- **Semi-automatic**: Different pattern analysis algorithms

### Server Performance Tuning
```json
{
  "Logging": {
    "SaveFrequency": 120.0,      // Reduce I/O frequency for busy servers
    "MaxBufferSizeKB": 512.0     // Increase buffer for high-traffic servers
  },
  "Recoil": {
    "AnalysisMinShots": 15       // Increase for more accurate detection
  }
}
```

## 🚀 Future Development

For detailed information about planned features, enhancements, and development roadmap, please see:

📋 **[TDAC Development Roadmap](TDAC_RoadMap.md)**

The roadmap includes:
- **Rapid Fire Detection** (Priority enhancement)
- **Movement Analysis** and **Speed Hack Detection**
- **Inventory Manipulation** monitoring
- **Building Analysis** for impossible placements
- **Statistics Dashboard** with web interface
- **Machine Learning Integration** for advanced pattern recognition
- **Real-time Alerts** via Discord/webhooks

**Current Version:** 2.4.0 | **Next Major Release:** 2.5.0 (Rapid Fire Detection)

## 📄 License

This project is open source. Please refer to the license file for details.

## 🤝 Contributing

Contributions are welcome! Please submit pull requests or issues through the repository.

## 📞 Support

For support, feature requests, or bug reports, please use the GitHub issues system.

---

**Important:** This plugin is designed for server administrators to monitor and detect potential cheating. It should be used in conjunction with other anti-cheat measures and manual investigation of flagged players. Always review violation logs manually before taking administrative action.
