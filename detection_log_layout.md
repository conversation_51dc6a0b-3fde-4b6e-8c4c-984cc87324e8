# TheDenAntiCheat Detection Log Layout

## Overview
The TheDenAntiCheat Rust Oxide plugin generates comprehensive CSV logs for all combat encounters and violations. The detection log captures both clean encounters and violations with detailed analysis data.

**Log Location**: `oxide/logs/thedenproject/detection_log_YYYYMMDD.csv`  
**File Rotation**: Daily at midnight UTC  
**Buffer System**: 256KB buffer with 300-second flush intervals  

## CSV Column Structure

The detection log contains **30 columns** with detailed information about each combat encounter or violation:

### Core Event Information

| Column | Description | Possible Values |
|--------|-------------|-----------------|
| **TIMESTAMP** | Precise timestamp of the event | Format: `yyyy-MM-dd HH:mm:ss.fff` (UTC) |
| **ENCOUNTER_ID** | Unique identifier for combat encounters | UUID string or `N/A` for direct violations |
| **PLAYER_ID** | Steam ID of the player being analyzed | 17-digit Steam ID string |
| **PLAYER_NAME** | Display name of the player | Player's current display name (commas replaced with semicolons) |

### Target Information

| Column | Description | Possible Values |
|--------|-------------|-----------------|
| **TARGET_ID** | Steam ID of the target player | 17-digit Steam ID string |
| **TARGET_NAME** | Display name of the target | Target's display name (commas replaced with semicolons) |

### Violation Analysis

| Column | Description | Possible Values |
|--------|-------------|-----------------|
| **VIOLATION_TYPE** | Category of violation detected | `NONE`, `RECOIL`, `AIMBOT`, `WALLHACK`, `MULTIPLE` |
| **WEAPON** | Weapon used in the encounter | Rust weapon short names (e.g., `rifle.ak`, `smg.mp5`, `pistol.m92`) |
| **ENCOUNTER_DURATION** | Length of the combat encounter | Float value in seconds (0.00 for direct violations) |
| **SHOT_COUNT** | Number of shots fired in the encounter | Integer count of shots |
| **WAS_DETECTED** | Whether a violation was detected | `True`, `False` |

### Weapon Database Information

| Column | Description | Possible Values |
|--------|-------------|-----------------|
| **WEAPON_HAS_DATA** | Whether weapon data is available | `True`, `False` |
| **WEAPON_EXPECTED_RECOIL_RANGE** | Expected recoil range for the weapon | Float value (0.000 if no data) |
| **WEAPON_FIRE_INTERVAL** | Time between shots for the weapon | Float value in seconds (0.000 if no data) |
| **WEAPON_RPM** | Rounds per minute for the weapon | Integer value (0 if no data) |
| **WEAPON_DISPLAY_NAME** | Human-readable weapon name | Display name (commas replaced with semicolons) |
| **WEAPON_YAW_MIN** | Minimum horizontal recoil bound | Float value (0.000 if no data) |
| **WEAPON_YAW_MAX** | Maximum horizontal recoil bound | Float value (0.000 if no data) |
| **WEAPON_PITCH_MIN** | Minimum vertical recoil bound | Float value (0.000 if no data) |
| **WEAPON_PITCH_MAX** | Maximum vertical recoil bound | Float value (0.000 if no data) |

### Configuration Settings

| Column | Description | Possible Values |
|--------|-------------|-----------------|
| **PATTERN_DETECTION_ENABLED** | Whether enhanced pattern detection is enabled | `True`, `False` |
| **CONFIG_VARIANCE_THRESHOLD** | Configured variance tolerance | Float value (default: 0.100) |
| **CONFIG_MAX_ANGLE_CHANGE** | Maximum allowed angle change (legacy) | Float value (default: 0.100) |

### Detection Analysis Data

| Column | Description | Possible Values |
|--------|-------------|-----------------|
| **DETECTION_METHOD** | Method used for recoil detection | `Enhanced`, `Legacy`, empty string |
| **ANGLE_CHANGES_LIST** | List of calculated angle changes | Semicolon-separated float values |
| **CALCULATED_MEAN** | Mean of angle changes | Float value (0.000 if not applicable) |
| **CALCULATED_VARIANCE** | Variance of angle changes | Float value (0.000 if not applicable) |
| **CALCULATED_STD_DEV** | Standard deviation of angle changes | Float value (0.000 if not applicable) |
| **CALCULATED_THRESHOLD** | Calculated detection threshold | Float value (0.000 if not applicable) |
| **PLAYER_RECOIL_RANGE** | Player's actual recoil range | Float value (0.000 if not applicable) |
| **PLAYER_MIN_RECOIL** | Minimum recoil value observed | Float value (0.000 if not applicable) |
| **PLAYER_MAX_RECOIL** | Maximum recoil value observed | Float value (0.000 if not applicable) |
| **DEVIATION_PERCENTAGE** | Percentage deviation from expected | Float value (0.0 if not applicable) |

## Violation Types Explained

### NONE
- **Description**: Clean encounter with no violations detected
- **VIOLATION_REASON**: `"Clean encounter"`
- **WAS_DETECTED**: `False`

### RECOIL
- **Description**: Recoil-based violations detected
- **Subtypes**:
  - **RecoilPattern**: Enhanced detection using weapon-specific data
  - **NoRecoilLegacy**: Legacy angle-based detection
- **WAS_DETECTED**: `True`

### AIMBOT
- **Description**: Excessive hit ratio detected
- **VIOLATION_REASON**: Contains hit ratio and threshold information
- **WAS_DETECTED**: `True`

### WALLHACK
- **Description**: Shots detected through solid obstacles
- **VIOLATION_REASON**: Contains obstacle information
- **WAS_DETECTED**: `True`

### MULTIPLE
- **Description**: Multiple violation types detected in single encounter
- **VIOLATION_REASON**: Semicolon-separated list of all violations
- **WAS_DETECTED**: `True`

## Detection Methods

### Enhanced Detection
- **Requirements**: Weapon data available AND pattern detection enabled
- **DETECTION_METHOD**: `"Enhanced"`
- **Analysis**: Uses weapon-specific recoil patterns and statistical analysis
- **Threshold**: Based on weapon's expected recoil range and variance tolerance

### Legacy Detection
- **Requirements**: No weapon data OR pattern detection disabled
- **DETECTION_METHOD**: `"Legacy"`
- **Analysis**: Basic angle change monitoring between consecutive shots
- **Threshold**: Fixed maximum angle change value

## Event Types

### ENCOUNTER_COMPLETE
- **Description**: Logged when a combat encounter ends (timeout, death, disconnect)
- **ENCOUNTER_ID**: Valid UUID
- **ENCOUNTER_DURATION**: Actual duration in seconds
- **Analysis**: Complete statistical analysis of the entire encounter

### VIOLATION_DETECTED
- **Description**: Logged immediately when a violation is detected
- **ENCOUNTER_ID**: `"N/A"`
- **ENCOUNTER_DURATION**: `0.00`
- **Analysis**: Real-time detection without encounter context

## Data Formats

### Numeric Precision
- **Float values**: 3 decimal places (e.g., `1.234`)
- **Duration**: 2 decimal places (e.g., `12.34`)
- **Percentage**: 1 decimal place (e.g., `85.5`)

### String Handling
- **Commas**: Replaced with semicolons to maintain CSV integrity
- **Empty values**: Represented as empty strings
- **Boolean values**: `True` or `False`

### Timestamp Format
- **Format**: `yyyy-MM-dd HH:mm:ss.fff`
- **Timezone**: UTC
- **Example**: `2024-06-14 15:30:45.123`

## Usage Examples

### Analyzing Recoil Violations
```sql
SELECT PLAYER_NAME, WEAPON, DETECTION_METHOD, CALCULATED_STD_DEV, DEVIATION_PERCENTAGE
FROM detection_log 
WHERE VIOLATION_TYPE = 'RECOIL' AND WAS_DETECTED = 'True'
ORDER BY DEVIATION_PERCENTAGE DESC;
```

### Finding Repeat Offenders
```sql
SELECT PLAYER_NAME, COUNT(*) as violation_count
FROM detection_log 
WHERE WAS_DETECTED = 'True'
GROUP BY PLAYER_NAME
ORDER BY violation_count DESC;
```

### Weapon-Specific Analysis
```sql
SELECT WEAPON, AVG(CALCULATED_STD_DEV) as avg_std_dev
FROM detection_log 
WHERE DETECTION_METHOD = 'Enhanced' AND WAS_DETECTED = 'False'
GROUP BY WEAPON;
```

## Notes

- **Buffer System**: Logs are buffered and flushed every 300 seconds or when buffer reaches 256KB
- **Daily Rotation**: New log files created at midnight UTC with YYYYMMDD format
- **Performance**: Minimal impact on server performance due to efficient buffering
- **Weapon Data**: Enhanced detection requires weapon data from RustDumper plugin
- **Clean Encounters**: All encounters are logged, not just violations, for comprehensive analysis
