# Requested folders to ignore
/oxide_reference/
/sample_log/
/sample_plugins/
/tools/

# macOS system files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows system files
Desktop.ini
$RECYCLE.BIN/

# IDE and editor files
.vscode/
.idea/
*.sublime-*
*.swp
*.swo
*~

# Visual Studio files
*.vs/
*.user
*.userosscache
*.sln.docstates

# Build artifacts and temporary files
*.tmp
*.temp
*.log
*.cache
*.bak
*.backup

# Plugin development files
*.dll
*.pdb
*.exe
bin/
obj/
Debug/
Release/

# Oxide/uMod specific
oxide/logs/
oxide/config/
oxide/data/
oxide/lang/
carbon/logs/
carbon/config/
carbon/data/

# Log files
*.log
logs/
*.csv

# Package manager files
node_modules/
packages/
*.lock

# Archive files
*.zip
*.rar
*.7z
*.tar
*.gz

# Documentation build outputs
docs/_build/
site/

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
bower_components/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port
